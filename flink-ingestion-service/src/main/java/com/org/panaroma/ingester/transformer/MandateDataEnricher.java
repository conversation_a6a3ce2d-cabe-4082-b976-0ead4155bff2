package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.CommonConstants.TERMINAL_STATUS_LIST;
import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.Constants.RelayConstants.IS_FROM_RELAY;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.NON_FINANCIAL_NON_CREATE_RECURRING_MANDATE_ACTION_KEYS;
import static com.org.panaroma.ingester.constants.Constants.PAYTM_HANDLE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.CREATE_DOC_NOT_FOUND_FOR_FIRST_EXEC_FAIL;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXCEPTION_WHILE_ENRICHING_MANDATE_DOC;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.LAST_FAILED_COLLECT_DOC_NOT_FOUND_WITH_SAME_EXEC_NO;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER;

import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.mandate.MandateBaseDto;
import com.org.panaroma.commons.dto.mandate.MandateInfoData;
import com.org.panaroma.commons.dto.mandate.MandateMetaData;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.enums.MandateMissingDataIdentifierEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import com.org.panaroma.commons.enums.MandateTypeEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.cache.AerospikeCacheClient;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.utils.MandateUtility;
import com.org.panaroma.ingester.utils.RetryUtility;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class MandateDataEnricher implements FlatMapFunction<MandateBaseDto, MandateBaseDto>, Serializable {

	private EsRepository esRepository;

	private AerospikeCacheClient aerospikeCacheClient;

	private RetryUtility retryUtility;

	private MetricsAgent metricsAgent;

	@Autowired
	public MandateDataEnricher(final EsRepository esRepository, final AerospikeCacheClient aerospikeCacheClient,
			final RetryUtility retryUtility, final MetricsAgent metricsAgent) {
		this.esRepository = esRepository;
		this.aerospikeCacheClient = aerospikeCacheClient;
		this.retryUtility = retryUtility;
		this.metricsAgent = metricsAgent;
	}

	@Override
	public void flatMap(final MandateBaseDto mandateBaseDto, final Collector<MandateBaseDto> collector) {

		if (Objects.isNull(mandateBaseDto)) {
			log.warn("Mandate data received for enrichment is null");
			return;
		}
		Set<MandateBaseDto> enrichedMandateDocs = new HashSet<>();
		MandateBaseDto storedMandateData;
		try {

			String cacheKey = mandateBaseDto.getCacheKey();

			// get already saved doc from aerospike (if any) for self merging
			storedMandateData = aerospikeCacheClient.getStoredMandateData(cacheKey, mandateBaseDto.getObjectType());
			if (Objects.isNull(storedMandateData)) {
				// get already saved doc from es (if any) for self merging
				storedMandateData = esRepository.getSelfMandateData(mandateBaseDto);
			}

			// Ignoring this check specially for backFilling txns, So that backFilling
			// flag gets saved in es in each case.
			// In case existing event is saved without ifr flag then also, current event
			// will be considered and saved in es for backfilling.
			if (!MandateUtility.isForMandateBackFilling(mandateBaseDto) && Objects.nonNull(storedMandateData)) {
				boolean isStoredEventFromRelay = isRelayEvent(storedMandateData);
				boolean isCurrentEventFromRelay = isRelayEvent(mandateBaseDto);

				if (!isStoredEventFromRelay && isCurrentEventFromRelay) {
					log.warn(
							"Ignoring current event as stored event is not from relay and current event is from relay for docId : {}",
							mandateBaseDto.docId());
					return;
				}
				if (isStoredEventFromRelay && !isCurrentEventFromRelay) {
					log.warn("Ignoring stored event as current event is for non-relay(tpap), for docId : {}",
							mandateBaseDto.docId());
					storedMandateData = null;
				}
			}

			// Special check to handle the case when info(parent) doc is not created with
			// create event, but now create event is received.
			// Change txnDate,createTxnId,createEventType of info doc with of current
			// create event.
			// Check comment over jira PR for jira :-
			// https://jira.mypaytm.com/browse/PTH-617
			if (MandateUtility.isForMandateBackFilling(mandateBaseDto)
					&& mandateBaseDto instanceof MandateInfoData currentInfoDoc && Objects.nonNull(storedMandateData)
					&& storedMandateData instanceof MandateInfoData storedInfoDoc) {
				if (MandateActionEnum.CREATE.getMandateActionKey().equals(currentInfoDoc.getCreateEventType())
						&& !MandateActionEnum.CREATE.getMandateActionKey().equals(storedInfoDoc.getCreateEventType())) {
					storedInfoDoc.setTxnDate(currentInfoDoc.getTxnDate());
					storedInfoDoc.setCreateEventType(MandateActionEnum.CREATE.getMandateActionKey());
					storedInfoDoc.setCreateTxnId(currentInfoDoc.getCreateTxnId());
					storedInfoDoc.setDocUpdatedDate(System.currentTimeMillis());
					MandateUtility.addMandateBackFillingIdentifierForAlteringParentInfo(storedMandateData);
					aerospikeCacheClient.saveMandateData(storedMandateData, storedMandateData.getCacheKey());
					collector.collect(storedMandateData);
					return;
				}
			}

			if (!mandateBaseDto.isSourceDtoUpdated(storedMandateData)) {
				return;
			}

			MandateActivityData mandateActivityData = null;
			if (mandateBaseDto instanceof MandateActivityData) {
				mandateActivityData = (MandateActivityData) mandateBaseDto;
			}

			if (Objects.nonNull(mandateActivityData)) {
				if (MandateTypeEnum.RECURRING_MANDATE.equals(mandateActivityData.getMandateType())) {
					updateEnrichedMandateDocsSetForRecurringMandate(mandateActivityData, enrichedMandateDocs);
				}
				if (MandateTypeEnum.IPO_MANDATE.equals(mandateActivityData.getMandateType())) {
					updateEnrichedMandateDocsSetForIpoMandate(mandateActivityData, enrichedMandateDocs);
				}
				if (MandateTypeEnum.SBMD_MANDATE.equals(mandateActivityData.getMandateType())) {
					updateEnrichedMandateDocsSetSbmdMandate(mandateActivityData, enrichedMandateDocs);
				}
				if (MandateTypeEnum.ONE_TIME_MANDATE.equals(mandateActivityData.getMandateType())) {
					updateEnrichedMandateDocsSetForOtmMandate(mandateActivityData, enrichedMandateDocs);
				}
			}
			Long currentTime = System.currentTimeMillis();
			mandateBaseDto.setDatesInMandateDoc(storedMandateData, currentTime);
			if (!enrichedMandateDocs.isEmpty()) {
				for (MandateBaseDto enrichedMandateDoc : enrichedMandateDocs) {
					enrichedMandateDoc.setDocUpdatedDate(currentTime);
					aerospikeCacheClient.saveMandateData(enrichedMandateDoc, enrichedMandateDoc.getCacheKey());
				}
			}
			aerospikeCacheClient.saveMandateData(mandateBaseDto, mandateBaseDto.getCacheKey());
			enrichedMandateDocs.add(mandateBaseDto);
			enrichedMandateDocs.forEach(collector::collect);
		}
		catch (Exception exception) {
			log.error(
					"Exception while enriching mandate data. Pushing data to mandate retry kafka topic."
							+ "mandateData : {}, Exception : {}",
					mandateBaseDto, CommonsUtility.exceptionFormatter(exception));
			metricsAgent.incrementCount(EXCEPTION_WHILE_ENRICHING_MANDATE_DOC);
			retryUtility.pushMandateDataToKafka(mandateBaseDto);
		}
	}

	private void updateEnrichedMandateDocsSetForRecurringMandate(final MandateActivityData mandateActivityData,
			final Set<MandateBaseDto> enrichedMandateDocs) throws Exception {
		if (NON_FINANCIAL_NON_CREATE_RECURRING_MANDATE_ACTION_KEYS.contains(mandateActivityData.getAction())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(mandateActivityData.getStatus())) {
			MandateInfoData updatedParentDoc = getUpdatedParentDocForNonFinancialNonCreateEvent(mandateActivityData);
			if (Objects.nonNull(updatedParentDoc)) {
				enrichedMandateDocs.add(updatedParentDoc);
			}
		}
		else if (MandateActionEnum.COLLECT.getMandateActionKey().equals(mandateActivityData.getAction())
				&& Objects.nonNull(mandateActivityData.getActionMetaData())
				&& Objects.nonNull(mandateActivityData.getActionMetaData().getExecutionNo())) {

			if (Objects.nonNull(mandateActivityData.getActionMetaData().getRetryAttempt())
					&& mandateActivityData.getActionMetaData().getRetryAttempt() > 0) {
				MandateActivityData lastDocForSameExecutionNo = getLastDocWithSameExecNoForRetryAttemptCase(
						mandateActivityData);
				if (Objects.nonNull(lastDocForSameExecutionNo)) {
					enrichedMandateDocs.add(lastDocForSameExecutionNo);
				}
			}
			if (ClientStatusEnum.FAILURE.getStatusKey().equals(mandateActivityData.getStatus())
					&& mandateActivityData.getActionMetaData().getExecutionNo() == 1
					&& StringUtils.isNotBlank(mandateActivityData.getMerchantVpa())
					&& mandateActivityData.getMerchantVpa().contains(PAYTM_HANDLE)) {
				MandateActivityData updatedCreateDocForFirstExecFailCase = getUpdatedCreateDocForFirstExecFailCase(
						mandateActivityData);
				if (Objects.nonNull(updatedCreateDocForFirstExecFailCase)) {
					enrichedMandateDocs.add(getUpdatedCreateDocForFirstExecFailCase(mandateActivityData));
				}
			}
		}
	}

	private void updateEnrichedMandateDocsSetForIpoMandate(final MandateActivityData mandateActivityData,
			final Set<MandateBaseDto> enrichedMandateDocs) throws Exception {

		if (isValidIpoEventForUpdatingParentDoc(mandateActivityData)) {
			MandateInfoData parentDoc = getParentDoc(mandateActivityData);
			if (Objects.nonNull(parentDoc)) {
				if (parentDoc.getUpdatedDate() < mandateActivityData.getUpdatedDate()) {
					parentDoc.setUpdatedDate(mandateActivityData.getUpdatedDate());
					MandateActionEnum action = MandateActionEnum
						.getMandateActionEnumByKey(mandateActivityData.getAction());
					switch (action) {
						case COLLECT:
							updateParentDocForIpoOrOtmMandateCollectEvent(mandateActivityData, parentDoc);
							break;
						case REVOKE:
							updateParentDocForIpoOrSbmdOrOtmMandateRevokeEvent(mandateActivityData, parentDoc);
							break;
						case UPDATE:
							updateParentDocForIpoMandateUpdateEvent(mandateActivityData, parentDoc);
							break;
						case EXPIRE:
							updateParentDocForIpoOrSbmdOrOtmMandateExpireEvent(mandateActivityData, parentDoc);
							break;
					}
					enrichedMandateDocs.add(parentDoc);
				}
				else {
					log.warn(
							"Updated Date of stored parent doc is greater than or equal to updated date of non financial non create event"
									+ " received for processing. So not updating the parent doc. current event : {}, parent event : {}",
							mandateActivityData, parentDoc);
					metricsAgent.incrementCount(UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER);
				}
			}
			else {
				log.warn("Parent doc is not present in ES for non CREATE event. mandate data : {}",
						mandateActivityData);
				metricsAgent.incrementCount(PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT);
			}
		}
	}

	private void updateEnrichedMandateDocsSetSbmdMandate(final MandateActivityData mandateActivityData,
			final Set<MandateBaseDto> enrichedMandateDocs) throws Exception {

		if (isValidSbmdEventForUpdatingParentDoc(mandateActivityData)) {
			MandateInfoData parentDoc = getParentDoc(mandateActivityData);
			if (Objects.nonNull(parentDoc)) {
				if (parentDoc.getUpdatedDate() < mandateActivityData.getUpdatedDate()) {
					parentDoc.setUpdatedDate(mandateActivityData.getUpdatedDate());
					MandateActionEnum action = MandateActionEnum
						.getMandateActionEnumByKey(mandateActivityData.getAction());
					switch (action) {
						case REVOKE:
							updateParentDocForIpoOrSbmdOrOtmMandateRevokeEvent(mandateActivityData, parentDoc);
							break;
						case EXPIRE:
							updateParentDocForIpoOrSbmdOrOtmMandateExpireEvent(mandateActivityData, parentDoc);
							break;
					}
					enrichedMandateDocs.add(parentDoc);
				}
				else {
					log.warn(
							"Updated Date of stored parent doc is greater than or equal to updated date of non financial non create event"
									+ " received for processing. So not updating the parent doc. current event : {}, parent event : {}",
							mandateActivityData, parentDoc);
					metricsAgent.incrementCount(UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER);
				}
			}
			else {
				log.warn("Parent doc is not present in ES for non CREATE event. mandate data : {}",
						mandateActivityData);
				metricsAgent.incrementCount(PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT);
			}
		}
	}

	private void updateEnrichedMandateDocsSetForOtmMandate(final MandateActivityData mandateActivityData,
			final Set<MandateBaseDto> enrichedMandateDocs) throws Exception {

		if (isValidOtmEventForUpdatingParentDoc(mandateActivityData)) {
			MandateInfoData parentDoc = getParentDoc(mandateActivityData);
			if (Objects.nonNull(parentDoc)) {
				if (parentDoc.getUpdatedDate() < mandateActivityData.getUpdatedDate()) {
					parentDoc.setUpdatedDate(mandateActivityData.getUpdatedDate());
					MandateActionEnum action = MandateActionEnum
						.getMandateActionEnumByKey(mandateActivityData.getAction());
					switch (action) {
						case COLLECT:
							updateParentDocForIpoOrOtmMandateCollectEvent(mandateActivityData, parentDoc);
							break;
						case REVOKE:
							updateParentDocForIpoOrSbmdOrOtmMandateRevokeEvent(mandateActivityData, parentDoc);
							break;
						case EXPIRE:
							updateParentDocForIpoOrSbmdOrOtmMandateExpireEvent(mandateActivityData, parentDoc);
							break;
					}
					enrichedMandateDocs.add(parentDoc);
				}
				else {
					log.warn("Updated Date of stored parent doc is greater than or equal to updated date of new event"
							+ " received for processing. So not updating the parent doc. current event : {}, parent event : {} for txnType : {}",
							mandateActivityData, parentDoc, mandateActivityData.getMandateType().name());
					metricsAgent.incrementCount(UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER);
				}
			}
			else {
				log.warn("Parent doc is not present in ES for non CREATE event. mandate data : {}",
						mandateActivityData);
				metricsAgent.incrementCount(PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT);
			}
		}
	}

	private void updateParentDocForIpoOrOtmMandateCollectEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		if (ClientStatusEnum.SUCCESS.getStatusKey().equals(mandateActivityData.getStatus())) {
			parentDoc.setStatus(MandateStatusEnum.COMPLETED.getMandateStatusKey());
			if (Objects.isNull(parentDoc.getMandateMetaData())) {
				parentDoc.setMandateMetaData(new MandateMetaData());
			}

			// Adding identifier for mandate backFilling.
			if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
				MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
			}
			parentDoc.getMandateMetaData().setAllotmentDate(mandateActivityData.getTxnDate());

		}
		else if (ClientStatusEnum.FAILURE.getStatusKey().equals(mandateActivityData.getStatus())) {
			parentDoc.setStatus(MandateStatusEnum.CANCELLED.getMandateStatusKey());
			if (Objects.isNull(parentDoc.getMandateMetaData())) {
				parentDoc.setMandateMetaData(new MandateMetaData());
			}

			// Adding identifier for mandate backFilling.
			if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
				MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
			}
			parentDoc.getMandateMetaData().setCancellationDate(mandateActivityData.getTxnDate());
		}
	}

	private void updateParentDocForIpoOrSbmdOrOtmMandateRevokeEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.CANCELLED.getMandateStatusKey());
		if (Objects.isNull(parentDoc.getMandateMetaData())) {
			parentDoc.setMandateMetaData(new MandateMetaData());
		}
		parentDoc.getMandateMetaData().setCancellationDate(mandateActivityData.getTxnDate());
		// Adding identifier for mandate backFilling.
		if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
			MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
		}
	}

	private void updateParentDocForIpoOrSbmdOrOtmMandateExpireEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.EXPIRED.getMandateStatusKey());

		Long expiryDate = null;
		if (Objects.nonNull(mandateActivityData.getActionMetaData())) {
			expiryDate = mandateActivityData.getActionMetaData().getExpiryDate();
		}

		Set<Integer> missingDataIdentifier = parentDoc.getMissingDataIdentifier();
		if (missingDataIdentifier == null || missingDataIdentifier.isEmpty()) {
			missingDataIdentifier = new HashSet<>();
		}

		if (Objects.isNull(expiryDate)) {
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT
				.getMissingDataIdentifierKey());
		}
		else {
			if (Objects.nonNull(parentDoc.getMandateMetaData())) {
				parentDoc.getMandateMetaData().setExpiryDate(expiryDate);
			}
			else {
				MandateMetaData mandateMetaData = new MandateMetaData();
				mandateMetaData.setExpiryDate(expiryDate);
				parentDoc.setMandateMetaData(mandateMetaData);
			}
		}
		if (!missingDataIdentifier.isEmpty()) {
			parentDoc.setMissingDataIdentifier(missingDataIdentifier);
		}
	}

	private void updateParentDocForIpoMandateUpdateEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setAmount(mandateActivityData.getAmount());

		if (Objects.isNull(parentDoc.getMandateMetaData())) {
			parentDoc.setMandateMetaData(new MandateMetaData());
		}

		if (Objects.nonNull(mandateActivityData.getActionMetaData())
				&& Objects.nonNull(mandateActivityData.getActionMetaData().getValidityEndDate())) {
			parentDoc.getMandateMetaData()
				.setValidityEndDate(mandateActivityData.getActionMetaData().getValidityEndDate());
		}

		// Adding identifier for mandate backFilling.
		if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
			MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
		}
	}

	private void updateParentDocForRecurringMandatePauseEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.PAUSED.getMandateStatusKey());
		if (Objects.nonNull(mandateActivityData.getActionMetaData())
				&& Objects.nonNull(mandateActivityData.getActionMetaData().getPauseEndDate())) {
			if (Objects.nonNull(parentDoc.getMandateMetaData())) {
				parentDoc.getMandateMetaData()
					.setPauseEndDate(mandateActivityData.getActionMetaData().getPauseEndDate());
			}
			else {
				MandateMetaData mandateMetaData = new MandateMetaData();
				mandateMetaData.setPauseEndDate(mandateActivityData.getActionMetaData().getPauseEndDate());
				parentDoc.setMandateMetaData(mandateMetaData);
			}
		}
	}

	private void updateParentDocForRecurringMandateRevokeEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.CANCELLED.getMandateStatusKey());
		/*
		 * Below statement is added because REVOKE event can be the next to PAUSE event.
		 * Its not the case that only UNPAUSE can be done after PAUSE
		 */
		if (Objects.nonNull(parentDoc.getMandateMetaData())) {
			parentDoc.getMandateMetaData().setPauseEndDate(null);
			parentDoc.getMandateMetaData().setCancellationDate(mandateActivityData.getTxnDate());
		}
		else {
			MandateMetaData mandateMetaData = new MandateMetaData();
			mandateMetaData.setCancellationDate(mandateActivityData.getTxnDate());
			parentDoc.setMandateMetaData(mandateMetaData);
		}
	}

	private void updateParentDocForRecurringMandateUpdateEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setAmount(mandateActivityData.getMandateAmount());

		if (Objects.isNull(parentDoc.getMandateMetaData())) {
			parentDoc.setMandateMetaData(new MandateMetaData());
		}

		if (Objects.nonNull(mandateActivityData.getActionMetaData())
				&& Objects.nonNull(mandateActivityData.getActionMetaData().getValidityEndDate())) {
			parentDoc.getMandateMetaData()
				.setValidityEndDate(mandateActivityData.getActionMetaData().getValidityEndDate());
		}
	}

	private void updateParentDocForRecurringMandateExpireEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.EXPIRED.getMandateStatusKey());

		Long expiryDate = null;
		if (Objects.nonNull(mandateActivityData.getActionMetaData())) {
			expiryDate = mandateActivityData.getActionMetaData().getExpiryDate();
		}

		Set<Integer> missingDataIdentifier = parentDoc.getMissingDataIdentifier();

		if (missingDataIdentifier.isEmpty()) {
			missingDataIdentifier = new HashSet<>();
		}

		if (Objects.isNull(expiryDate)) {
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT
				.getMissingDataIdentifierKey());
		}
		/*
		 * Below statement is added because EXPIRE event can be the next to PAUSE event.
		 * Its not the case that only UNPAUSE can be done after PAUSE
		 */
		if (Objects.nonNull(parentDoc.getMandateMetaData())) {
			parentDoc.getMandateMetaData().setPauseEndDate(null);
			parentDoc.getMandateMetaData().setExpiryDate(expiryDate);
		}
		else {
			MandateMetaData mandateMetaData = new MandateMetaData();
			mandateMetaData.setExpiryDate(expiryDate);
			parentDoc.setMandateMetaData(mandateMetaData);
		}
		if (!missingDataIdentifier.isEmpty()) {
			parentDoc.setMissingDataIdentifier(missingDataIdentifier);
		}

	}

	private void updateParentDocForRecurringMandateUnpauseEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {
		parentDoc.setStatus(MandateStatusEnum.ACTIVE.getMandateStatusKey());
		if (Objects.nonNull(parentDoc.getMandateMetaData())) {
			parentDoc.getMandateMetaData().setPauseEndDate(null);
		}
	}

	/**
	 * Updates parent document for recurring mandate Port-In events. Sets status to
	 * ACTIVE, stores otherUMN, and clears pause metadata.
	 * @param mandateActivityData The mandate activity data containing port-in details
	 * @param parentDoc The parent mandate document to update
	 */
	private void updateParentDocForRecurringMandatePortInEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {

		// Update common fields
		parentDoc.setDocUpdatedDate(System.currentTimeMillis());
		parentDoc.setStatus(MandateStatusEnum.ACTIVE.getMandateStatusKey());

		Set<Integer> missingDataIdentifier = parentDoc.getMissingDataIdentifier();
		if (missingDataIdentifier == null || missingDataIdentifier.isEmpty()) {
			missingDataIdentifier = new HashSet<>();
		}

		if (!missingDataIdentifier.isEmpty()) {
			parentDoc.setMissingDataIdentifier(missingDataIdentifier);
		}
	}

	/**
	 * Updates parent document for recurring mandate Port-Out events. Sets status to
	 * PORTED, stores otherUMN, and sets cancellation date.
	 * @param mandateActivityData The mandate activity data containing port-out details
	 * @param parentDoc The parent mandate document to update
	 */
	private void updateParentDocForRecurringMandatePortOutEvent(final MandateActivityData mandateActivityData,
			final MandateInfoData parentDoc) {

		// Update common fields
		parentDoc.setDocUpdatedDate(System.currentTimeMillis());
		parentDoc.setStatus(MandateStatusEnum.PORTED.getMandateStatusKey());

		Set<Integer> missingDataIdentifier = parentDoc.getMissingDataIdentifier();
		if (missingDataIdentifier == null || missingDataIdentifier.isEmpty()) {
			missingDataIdentifier = new HashSet<>();
		}

		if (!missingDataIdentifier.isEmpty()) {
			parentDoc.setMissingDataIdentifier(missingDataIdentifier);
		}
	}

	private boolean isValidIpoEventForUpdatingParentDoc(final MandateActivityData mandateActivityData) {
		return (MandateActionEnum.COLLECT.getMandateActionKey().equals(mandateActivityData.getAction())
				&& TERMINAL_STATUS_LIST.contains(mandateActivityData.getStatus()))
				|| ((MandateActionEnum.EXPIRE.getMandateActionKey().equals(mandateActivityData.getAction())
						|| MandateActionEnum.UPDATE.getMandateActionKey().equals(mandateActivityData.getAction())
						|| MandateActionEnum.REVOKE.getMandateActionKey().equals(mandateActivityData.getAction()))
						&& ClientStatusEnum.SUCCESS.getStatusKey().equals(mandateActivityData.getStatus()));
	}

	private boolean isValidSbmdEventForUpdatingParentDoc(final MandateActivityData mandateActivityData) {
		return (MandateActionEnum.EXPIRE.getMandateActionKey().equals(mandateActivityData.getAction())
				|| MandateActionEnum.REVOKE.getMandateActionKey().equals(mandateActivityData.getAction()))
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(mandateActivityData.getStatus());
	}

	private boolean isValidOtmEventForUpdatingParentDoc(final MandateActivityData mandateActivityData) {
		return (MandateActionEnum.COLLECT.getMandateActionKey().equals(mandateActivityData.getAction())
				&& TERMINAL_STATUS_LIST.contains(mandateActivityData.getStatus()))
				|| ((MandateActionEnum.EXPIRE.getMandateActionKey().equals(mandateActivityData.getAction())
						|| MandateActionEnum.REVOKE.getMandateActionKey().equals(mandateActivityData.getAction()))
						&& ClientStatusEnum.SUCCESS.getStatusKey().equals(mandateActivityData.getStatus()));
	}

	private boolean isRelayEvent(final MandateBaseDto mandateBaseDto) {
		if (ObjectUtils.isEmpty(mandateBaseDto.getMetaDataMap())) {
			return false;
		}
		if (mandateBaseDto.getMetaDataMap().containsKey(IS_FROM_RELAY)) {
			return true;
		}
		return false;
	}

	private MandateActivityData getUpdatedCreateDocForFirstExecFailCase(final MandateActivityData mandateActivityData)
			throws Exception {
		MandateActivityData createDoc;
		String cacheKeyForCreateDoc = mandateActivityData.getEntityId() + PIPE_SYMBOL
				+ mandateActivityData.getCreateTxnId();
		createDoc = (MandateActivityData) aerospikeCacheClient.getStoredMandateData(cacheKeyForCreateDoc,
				mandateActivityData.getObjectType());
		if (Objects.isNull(createDoc)) {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("entityId", mandateActivityData.getEntityId());
			paramMap.put("umn", mandateActivityData.getUmn());
			paramMap.put("txnId", mandateActivityData.getCreateTxnId());
			createDoc = (MandateActivityData) esRepository.fetchMandateDataFromAlias(paramMap,
					mandateActivityData.getEntityId(), false);
		}
		if (Objects.nonNull(createDoc)) {
			createDoc.setViewStatus(ClientStatusEnum.FAILURE.getStatusKey());

			// Adding mandate backFilling identifier.
			if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
				MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(createDoc);
			}
			return createDoc;
		}
		else {
			log.warn("CREATE child doc is not present in ES for first execution event. mandate data : {}",
					mandateActivityData);
			metricsAgent.incrementCount(CREATE_DOC_NOT_FOUND_FOR_FIRST_EXEC_FAIL);
		}
		return null;
	}

	private MandateInfoData getUpdatedParentDocForNonFinancialNonCreateEvent(
			final MandateActivityData mandateActivityData) throws Exception {
		MandateInfoData parentDoc = getParentDoc(mandateActivityData);
		if (Objects.nonNull(parentDoc)) {
			if (parentDoc.getUpdatedDate() < mandateActivityData.getUpdatedDate()) {
				parentDoc.setUpdatedDate(mandateActivityData.getUpdatedDate());
				MandateActionEnum action = MandateActionEnum.getMandateActionEnumByKey(mandateActivityData.getAction());
				switch (action) {
					case PAUSE:
						updateParentDocForRecurringMandatePauseEvent(mandateActivityData, parentDoc);
						break;
					case UNPAUSE:
						updateParentDocForRecurringMandateUnpauseEvent(mandateActivityData, parentDoc);
						break;
					case REVOKE:
						updateParentDocForRecurringMandateRevokeEvent(mandateActivityData, parentDoc);
						break;
					case UPDATE:
						updateParentDocForRecurringMandateUpdateEvent(mandateActivityData, parentDoc);
						break;
					case EXPIRE:
						updateParentDocForRecurringMandateExpireEvent(mandateActivityData, parentDoc);
						break;
					case IN_PORT:
						updateParentDocForRecurringMandatePortInEvent(mandateActivityData, parentDoc);
						break;
					case OUT_PORT:
						updateParentDocForRecurringMandatePortOutEvent(mandateActivityData, parentDoc);
						break;
				}

				// Adding mandate backFilling identifier.
				if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
					MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(parentDoc);
				}
				return parentDoc;
			}
			else {
				log.warn(
						"Updated Date of stored parent doc is greater than or equal to updated date of non financial non create event"
								+ " received for processing. So not updating the parent doc. current event : {}, parent event : {}",
						mandateActivityData, parentDoc);
				metricsAgent.incrementCount(UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER);
			}
		}
		else {
			log.warn("Parent doc is not present in ES for non CREATE event. mandate data : {}", mandateActivityData);
			metricsAgent.incrementCount(PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT);
		}
		return null;
	}

	private MandateActivityData getLastDocWithSameExecNoForRetryAttemptCase(
			final MandateActivityData mandateActivityData) throws Exception {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("entityId", mandateActivityData.getEntityId());
		paramMap.put("umn", mandateActivityData.getUmn());
		paramMap.put("actionMetaData.executionNo", mandateActivityData.getActionMetaData().getExecutionNo());
		if (mandateActivityData.getActionMetaData().getRetryAttempt() > 1) {
			paramMap.put("actionMetaData.retryAttempt", mandateActivityData.getActionMetaData().getRetryAttempt() - 1);
		}
		else {
			/*
			 * Below check is added for a case where we want to get first execution
			 * failure doc when retryAttempt = 1 event arrives. We can't proceed without
			 * this must_not query because the query can fetch 2 docs at the same time : 1
			 * -> actual doc which we want 2 -> non-terminal doc with same txnId which we
			 * don't want
			 */
			paramMap.put("mustNotTxnId", mandateActivityData.getTxnId());
		}

		MandateActivityData lastDocForSameExecutionNo = (MandateActivityData) esRepository
			.fetchMandateDataFromAlias(paramMap, mandateActivityData.getEntityId(), false);
		if (Objects.nonNull(lastDocForSameExecutionNo)) {
			lastDocForSameExecutionNo.setShowInHistory(Boolean.FALSE);

			// Adding mandate backFilling identifier.
			if (MandateUtility.isForMandateBackFilling(mandateActivityData)) {
				MandateUtility.addMandateBackFillingIdentifierForRelativeDoc(lastDocForSameExecutionNo);
			}

			return lastDocForSameExecutionNo;
		}
		log.error("Last failed collect doc not found in es for same" + "executionNo. mandate data : {}",
				mandateActivityData);
		metricsAgent.incrementCount(LAST_FAILED_COLLECT_DOC_NOT_FOUND_WITH_SAME_EXEC_NO);
		return null;
	}

	private MandateInfoData getParentDoc(final MandateActivityData mandateActivityData) throws Exception {
		String cacheKeyForParent = mandateActivityData.getEntityId() + PIPE_SYMBOL + mandateActivityData.getUmn();
		MandateInfoData parentDoc = (MandateInfoData) aerospikeCacheClient.getStoredMandateData(cacheKeyForParent,
				new TypeReference<MandateInfoData>() {
				});
		if (Objects.isNull(parentDoc)) {
			return (MandateInfoData) esRepository.getMandateData(mandateActivityData, true, false);
		}
		return parentDoc;
	}

}
