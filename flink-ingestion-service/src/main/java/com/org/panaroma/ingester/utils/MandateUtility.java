package com.org.panaroma.ingester.utils;

import static com.org.panaroma.commons.constants.CommonConstants.IS_BACKFILLING_FOR_MANDATE_DB;
import static com.org.panaroma.commons.constants.CommonConstants.TRUE;
import static com.org.panaroma.commons.constants.Constants.BACK_FILLING_IDENTIFIER_MANDATE;
import static com.org.panaroma.commons.constants.Constants.RRN;
import static com.org.panaroma.commons.constants.Constants.RelayConstants.IS_FROM_RELAY;
import static com.org.panaroma.commons.constants.Constants.UNDERSCORE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.*;
import static com.org.panaroma.commons.constants.WebConstants.OTHER_UMN;
import static com.org.panaroma.commons.constants.WebConstants.UPI_VIA_CC_FLAG;
import static com.org.panaroma.commons.constants.WebConstants.UpiErrorCodes.ERROR_CODE;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.mandate.*;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.enums.MandateBackFillingIdentifierEnum;
import com.org.panaroma.commons.enums.MandateMissingDataIdentifierEnum;
import com.org.panaroma.commons.enums.MandatePaymentSystemEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import com.org.panaroma.commons.enums.MandateTypeEnum;
import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.Utility;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class MandateUtility implements Serializable {

	/**
	 * Maps a TransactionHistoryDetails object to a MandateActivityData object.
	 * <p>
	 * This method extracts relevant mandate activity information from the transaction
	 * history, including participant details, action/status, metadata, error codes, and
	 * mandate type-specific metadata. It also tracks missing/required data for downstream
	 * consumers.
	 * @param thd TransactionHistoryDetails input object (source of truth for mandate
	 * activity)
	 * @return MandateActivityData object populated with mapped fields
	 */
	public static MandateActivityData mapThdToMandateActivityData(final TransactionHistoryDetails thd) {
		MandateActivityData mandateActivityData = new MandateActivityData();

		// 1. Extract entityId (payer) or merchantVpa (payee) from participants
		for (TxnParticipants participant : thd.getParticipants()) {
			if (StringUtils.isNotBlank(participant.getCustomerId())) {
				// Set entityId if participant is a customer
				mandateActivityData.setEntityId(participant.getCustomerId());
			}
			else if (Objects.nonNull(participant.getMerchantData()) && Objects.nonNull(participant.getUpiData())
					&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
				// Set merchantVpa if participant is a merchant with VPA
				mandateActivityData.setMerchantVpa(participant.getUpiData().getVpa());
			}
		}

		// 2. Determine mandate action and status
		MandateActionEnum mandateAction = com.org.panaroma.commons.utility.MandateUtility
			.getMandateAction(thd.getContextMap());
		mandateActivityData.setAction(mandateAction.getMandateActionKey());
		mandateActivityData.setStatus(thd.getStatus().getStatusKey());
		// For consistency, viewStatus is set to status (may differ for some mandate
		// types)
		mandateActivityData.setViewStatus(mandateActivityData.getStatus());

		// 3. Set basic identifiers
		mandateActivityData.setUmn(thd.getUmn());
		mandateActivityData.setTxnId(thd.getSystemId());

		// 4. Populate metaDataMap if IS_FROM_RELAY is present in contextMap
		if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(IS_FROM_RELAY))) {
			if (ObjectUtils.isEmpty(mandateActivityData.getMetaDataMap())) {
				mandateActivityData.setMetaDataMap(new HashMap<>());
			}
			mandateActivityData.getMetaDataMap().put(IS_FROM_RELAY, thd.getContextMap().get(IS_FROM_RELAY));
		}

		// 5. Add backfilling identifier if this is a backfilling event
		if (MandateUtility.isForMandateBackFilling(thd)) {
			addMandateBackFillingIdentifier(mandateActivityData);
		}

		// 6. Track missing data identifiers
		Set<Integer> missingDataIdentifier = new HashSet<>();

		// Store otherUMN for port events
		if (StringUtils.isNotBlank(thd.getContextMap().get(OTHER_UMN))) {
			mandateActivityData.setOtherUMN(thd.getContextMap().get(OTHER_UMN));
		}
		else if (MandateActionEnum.IN_PORT.equals(mandateAction) || MandateActionEnum.OUT_PORT.equals(mandateAction)) {
			// Only track as missing for port events where otherUMN is expected
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.OTHER_UMN_MISSING.getMissingDataIdentifierKey());
		}

		// Set RRN if present, else track as missing
		if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(RRN))) {
			mandateActivityData.setRrn(thd.getContextMap().get(RRN));
		}
		else {
			log.warn("MissingData : rrn is missing in contextMap. thd : {}", thd);
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.RRN_MISSING.getMissingDataIdentifierKey());
		}

		// 7. Set amount and mandateAmount fields based on mandate type and action
		setAmountFieldsInMandateActivityData(mandateActivityData, thd, mandateAction, missingDataIdentifier);

		// 8. Set errorCode for failure events, else track as missing
		if (ClientStatusEnum.FAILURE.equals(thd.getStatus())) {
			if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(ERROR_CODE))) {
				mandateActivityData.setErrorCode(thd.getContextMap().get(ERROR_CODE));
			}
			else {
				log.warn("MissingData : errorCode is missing for failure event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.ERROR_CODE_MISSING_FOR_FAILURE_EVENT
					.getMissingDataIdentifierKey());
			}
		}

		// 9. Populate action metadata based on mandate type
		ActionMetadata actionMetadata = null;
		TransactionTypeEnum txnType = thd.getTxnType();
		if (txnType != null) {
			switch (txnType) {
				case RECURRING_MANDATE ->
					actionMetadata = getActionMetadataForRecurringMandate(thd, mandateAction, missingDataIdentifier);
				case IPO_MANDATE, ONE_TIME_MANDATE, SBMD_MANDATE ->
					actionMetadata = getActionMetadataForIpoSbmdOtmMandate(thd, mandateAction, missingDataIdentifier);
			}
		}
		// If this is a backfill transaction, mark metadata accordingly
		if (com.org.panaroma.commons.utility.MandateUtility.isMandateBackFillTxn(thd) && actionMetadata != null) {
			actionMetadata.setIsBackFilled(true);
		}
		mandateActivityData.setActionMetaData(actionMetadata);

		// 10. Attach missing data identifiers if any
		if (!missingDataIdentifier.isEmpty()) {
			mandateActivityData.setMissingDataIdentifier(missingDataIdentifier);
		}

		// 11. Set parent transaction ID (createTxnId), showInHistory flag, and timestamps
		mandateActivityData.setCreateTxnId(thd.getParentTxnId());
		mandateActivityData.setShowInHistory(Boolean.TRUE);
		mandateActivityData.setTxnDate(Long.parseLong(thd.getTxnDate()));
		mandateActivityData.setUpdatedDate(Long.parseLong(thd.getUpdatedDate()));

		// 12. Set mandate type based on transaction type
		MandateTypeEnum mandateType = null;
		if (txnType != null) {
			switch (txnType) {
				case RECURRING_MANDATE -> mandateType = MandateTypeEnum.RECURRING_MANDATE;
				case IPO_MANDATE -> mandateType = MandateTypeEnum.IPO_MANDATE;
				case SBMD_MANDATE -> mandateType = MandateTypeEnum.SBMD_MANDATE;
				case ONE_TIME_MANDATE -> mandateType = MandateTypeEnum.ONE_TIME_MANDATE;
			}
		}
		mandateActivityData.setMandateType(mandateType);

		return mandateActivityData;
	}

	/**
	 * Builds ActionMetadata for IPO, SBMD, and One-Time Mandate transactions. Extracts
	 * validity end date and expiry date (for EXPIRE action) from contextMap. Tracks
	 * missing data identifiers for required fields.
	 * @param thd TransactionHistoryDetails source object
	 * @param mandateAction MandateActionEnum for this transaction
	 * @param missingDataIdentifier Set to collect missing data keys
	 * @return Populated ActionMetadata object
	 */
	private static ActionMetadata getActionMetadataForIpoSbmdOtmMandate(final TransactionHistoryDetails thd,
			final MandateActionEnum mandateAction, final Set<Integer> missingDataIdentifier) {
		ActionMetadata actionMetadata = new ActionMetadata();
		// Extract validity end date
		String validityEndDate = Utility.getContextMapValue(thd, VALIDITY_END_DATE);
		if (StringUtils.isNotBlank(validityEndDate)) {
			actionMetadata.setValidityEndDate(Long.parseLong(validityEndDate));
		}
		else {
			log.warn("MissingData : ValidityEndDate is missing for mandate event. thd : {}", thd);
			missingDataIdentifier
				.add(MandateMissingDataIdentifierEnum.VALIDITY_END_DATE_MISSING.getMissingDataIdentifierKey());
		}
		// Extract expiry date for EXPIRE action
		if (MandateActionEnum.EXPIRE.equals(mandateAction)) {
			String expireOn = Utility.getContextMapValue(thd, EXPIRE_ON);
			if (StringUtils.isNotBlank(expireOn)) {
				actionMetadata.setExpiryDate(DateTimeUtility.getEpochMillisForWeekMonthDayTimeFormat(expireOn));
			}
			else {
				log.warn("MissingData : expiryOnDate is missing for expiry event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT
					.getMissingDataIdentifierKey());
			}
		}
		return actionMetadata;
	}

	private static ActionMetadata getActionMetadataForRecurringMandate(final TransactionHistoryDetails thd,
			final MandateActionEnum mandateAction, final Set<Integer> missingDataIdentifier) {
		ActionMetadata actionMetadata = new ActionMetadata();
		if (Objects.nonNull(thd.getContextMap())
				&& StringUtils.isNotBlank(thd.getContextMap().get(VALIDITY_END_DATE))) {
			actionMetadata.setValidityEndDate(Long.parseLong(thd.getContextMap().get(VALIDITY_END_DATE)));
		}
		else {
			log.warn("MissingData : ValidityEndDate is missing for mandate event. thd : {}", thd);
			missingDataIdentifier
				.add(MandateMissingDataIdentifierEnum.VALIDITY_END_DATE_MISSING.getMissingDataIdentifierKey());
		}

		if (MandateActionEnum.COLLECT.equals(mandateAction)) {

			if (Objects.nonNull(thd.getContextMap())) {
				if (StringUtils.isNotBlank(thd.getContextMap().get(EXECUTION_NO))) {
					actionMetadata.setExecutionNo(Integer.parseInt(thd.getContextMap().get(EXECUTION_NO)));
				}
				else {
					log.warn("MissingData : executionNo is missing for collect event. thd : {}", thd);
					missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXECUTION_NO_MISSING_FOR_COLLECT_EVENT
						.getMissingDataIdentifierKey());
				}
				if (StringUtils.isNotBlank(thd.getContextMap().get(RETRY_ATTEMPT))) {
					actionMetadata.setRetryAttempt(Integer.parseInt(thd.getContextMap().get(RETRY_ATTEMPT)));
				}
			}
			else {
				log.warn("MissingData : contextMap missing for collect event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXECUTION_NO_MISSING_FOR_COLLECT_EVENT
					.getMissingDataIdentifierKey());
			}
		}

		if (MandateActionEnum.PAUSE.equals(mandateAction)) {
			if (Objects.nonNull(thd.getContextMap())
					&& StringUtils.isNotBlank(thd.getContextMap().get(PAUSE_END_DATE))) {
				actionMetadata.setPauseEndDate(Long.parseLong(thd.getContextMap().get(PAUSE_END_DATE)));
			}
			else {
				log.warn("MissingData : PauseEndDate is missing for pause event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.PAUSE_END_DATE_MISSING_FOR_PAUSE_EVENT
					.getMissingDataIdentifierKey());
			}
		}
		if (MandateActionEnum.EXPIRE.equals(mandateAction)) {
			if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(EXPIRE_ON))) {
				actionMetadata.setExpiryDate(
						DateTimeUtility.getEpochMillisForWeekMonthDayTimeFormat(thd.getContextMap().get(EXPIRE_ON)));
			}
			else {
				log.warn("MissingData : expiryOnDate is missing for expiry event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT
					.getMissingDataIdentifierKey());
			}
		}
		return actionMetadata;
	}

	private static void setAmountFieldsInMandateActivityData(final MandateActivityData mandateActivityData,
			final TransactionHistoryDetails thd, final MandateActionEnum mandateAction,
			final Set<Integer> missingDataIdentifier) {
		// Added RECURRING_MANDATE check because mandateAmount only makes sense in
		// RECURRING Mandate txns.
		if (TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
			if (MandateActionEnum.COLLECT.equals(mandateAction)) {
				mandateActivityData.setAmount(Double.parseDouble(thd.getAmount()));
				if (ObjectUtils.isNotEmpty(thd.getContextMap()) && thd.getContextMap().containsKey(MANDATE_AMOUNT)) {
					mandateActivityData.setMandateAmount(Double.parseDouble(thd.getContextMap().get(MANDATE_AMOUNT)));
				}
				else {
					log.warn("MissingData : mandateAmount is missing for collect event. thd : {}", thd);
					missingDataIdentifier.add(MandateMissingDataIdentifierEnum.MANDATE_AMOUNT_MISSING_FOR_COLLECT_EVENT
						.getMissingDataIdentifierKey());
				}
			}
			else {
				mandateActivityData.setMandateAmount(Double.parseDouble(thd.getAmount()));
			}
		}
		else {
			mandateActivityData.setAmount(Double.parseDouble(thd.getAmount()));
		}
	}

	public static MandateInfoData mapThdToMandateInfoData(final TransactionHistoryDetails thd) {

		MandateInfoData mandateInfoData = new MandateInfoData();
		Set<Integer> missingDataIdentifier = new HashSet<>();
		mandateInfoData.setCreateTxnId(thd.getSystemId());

		MandateActionEnum mandateAction = com.org.panaroma.commons.utility.MandateUtility
			.getMandateAction(thd.getContextMap());

		if (Objects.nonNull(mandateAction)) {
			mandateInfoData.setCreateEventType(mandateAction.getMandateActionKey());
		}

		if (TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())
				&& MandateActionEnum.COLLECT.equals(mandateAction)) {
			if (ObjectUtils.isNotEmpty(thd.getContextMap()) && thd.getContextMap().containsKey(MANDATE_AMOUNT)) {
				mandateInfoData.setAmount(Double.parseDouble(thd.getContextMap().get(MANDATE_AMOUNT)));
			}
			else {
				log.warn("MissingData : mandateAmount is missing in collect event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.MANDATE_AMOUNT_MISSING_FOR_COLLECT_EVENT
					.getMissingDataIdentifierKey());
			}
		}
		else {
			mandateInfoData.setAmount(Double.parseDouble(thd.getAmount()));
		}

		mandateInfoData.setType(TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())
				? MandateTypeEnum.RECURRING_MANDATE.getMandateTypeKey()
				: TransactionTypeEnum.IPO_MANDATE.equals(thd.getTxnType())
						? MandateTypeEnum.IPO_MANDATE.getMandateTypeKey()
						: TransactionTypeEnum.SBMD_MANDATE.equals(thd.getTxnType())
								? MandateTypeEnum.SBMD_MANDATE.getMandateTypeKey()
								: TransactionTypeEnum.ONE_TIME_MANDATE.equals(thd.getTxnType())
										? MandateTypeEnum.ONE_TIME_MANDATE.getMandateTypeKey() : null);

		MandateStatusEnum mandateStatus = getMandateStatus(thd);
		if (Objects.nonNull(mandateStatus)) {
			mandateInfoData.setStatus(mandateStatus.getMandateStatusKey());
		}
		mandateInfoData.setUmn(thd.getUmn());

		if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(IS_FROM_RELAY))) {
			if (ObjectUtils.isEmpty(mandateInfoData.getMetaDataMap())) {
				mandateInfoData.setMetaDataMap(new HashMap<>());
			}
			mandateInfoData.getMetaDataMap().put(IS_FROM_RELAY, thd.getContextMap().get(IS_FROM_RELAY));
		}

		if (MandateUtility.isForMandateBackFilling(thd)) {
			addMandateBackFillingIdentifier(mandateInfoData);
		}

		MandateMetaData mandateMetaData = getMandateMetaData(thd, mandateAction, missingDataIdentifier);

		if (com.org.panaroma.commons.utility.MandateUtility.isMandateBackFillTxn(thd)) {
			mandateMetaData.setIsBackFilled(true);
		}

		mandateInfoData.setMandateMetaData(mandateMetaData);

		mandateInfoData.setTxnDate(Long.parseLong(thd.getTxnDate()));
		mandateInfoData.setUpdatedDate(Long.parseLong(thd.getUpdatedDate()));

		for (TxnParticipants participant : thd.getParticipants()) {
			if (StringUtils.isNotBlank(participant.getCustomerId())) {
				mandateInfoData.setEntityId(participant.getCustomerId());
				mandateInfoData.setPayerData(getPayerData(thd, participant, missingDataIdentifier));
			}
			else {
				mandateInfoData.setPayeeData(getPayeeData(thd, participant, missingDataIdentifier));
			}
		}

		if (!missingDataIdentifier.isEmpty()) {
			mandateInfoData.setMissingDataIdentifier(missingDataIdentifier);
		}

		return mandateInfoData;

	}

	private static MandateMetaData getMandateMetaData(final TransactionHistoryDetails thd,
			final MandateActionEnum mandateAction, final Set<Integer> missingDataIdentifier) {
		MandateMetaData mandateMetaData = new MandateMetaData();
		if (Objects.nonNull(thd.getContextMap())
				&& StringUtils.isNotBlank(thd.getContextMap().get(VALIDITY_START_DATE))) {
			mandateMetaData.setValidityStartDate(Long.parseLong(thd.getContextMap().get(VALIDITY_START_DATE)));
		}
		else {
			log.warn("MissingData : ValidityStartDate is missing in mandate event. thd : {}", thd);
			missingDataIdentifier
				.add(MandateMissingDataIdentifierEnum.VALIDITY_START_DATE_MISSING.getMissingDataIdentifierKey());
		}
		if (Objects.nonNull(thd.getContextMap())
				&& StringUtils.isNotBlank(thd.getContextMap().get(VALIDITY_END_DATE))) {
			mandateMetaData.setValidityEndDate(Long.parseLong(thd.getContextMap().get(VALIDITY_END_DATE)));
		}
		else {
			log.warn("MissingData : ValidityEndDate is missing in mandate event. thd : {}", thd);
			missingDataIdentifier
				.add(MandateMissingDataIdentifierEnum.VALIDITY_END_DATE_MISSING.getMissingDataIdentifierKey());
		}

		if (MandateActionEnum.REVOKE.equals(mandateAction) && ClientStatusEnum.SUCCESS.equals(thd.getStatus())) {
			mandateMetaData.setCancellationDate(Long.parseLong(thd.getTxnDate()));
		}

		if (MandateActionEnum.EXPIRE.equals(mandateAction)) {
			if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(EXPIRE_ON))) {
				mandateMetaData.setExpiryDate(
						DateTimeUtility.getEpochMillisForWeekMonthDayTimeFormat(thd.getContextMap().get(EXPIRE_ON)));
			}
			else {
				log.warn("MissingData : expiryOnDate is missing for expiry event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT
					.getMissingDataIdentifierKey());
			}
		}

		if (TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
			populateRmSpecificFieldsInMandateMetaData(thd, mandateMetaData, mandateAction, missingDataIdentifier);
		}

		if (TransactionTypeEnum.IPO_MANDATE.equals(thd.getTxnType())
				|| TransactionTypeEnum.ONE_TIME_MANDATE.equals(thd.getTxnType())) {
			populateIpoAndOtmMandateSpecificFieldsInMandateMetaData(thd, mandateAction, mandateMetaData);
		}
		return mandateMetaData;
	}

	private static void populateRmSpecificFieldsInMandateMetaData(final TransactionHistoryDetails thd,
			final MandateMetaData mandateMetaData, final MandateActionEnum mandateAction,
			final Set<Integer> missingDataIdentifier) {
		if (Objects.nonNull(thd.getContextMap()) && StringUtils.isNotBlank(thd.getContextMap().get(FREQUENCY))) {
			mandateMetaData.setFrequency(thd.getContextMap().get(FREQUENCY));
		}
		else {
			log.warn("MissingData : Frequency is missing in mandate event. thd : {}", thd);
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.FREQUENCY_MISSING.getMissingDataIdentifierKey());
		}

		if (MandateActionEnum.PAUSE.equals(mandateAction) && ClientStatusEnum.SUCCESS.equals(thd.getStatus())) {
			if (Objects.nonNull(thd.getContextMap())
					&& StringUtils.isNotBlank(thd.getContextMap().get(PAUSE_END_DATE))) {
				mandateMetaData.setPauseEndDate(Long.parseLong(thd.getContextMap().get(PAUSE_END_DATE)));
			}
			else {
				log.warn("MissingData : PauseEndDate is missing for pause event. thd : {}", thd);
				missingDataIdentifier.add(MandateMissingDataIdentifierEnum.PAUSE_END_DATE_MISSING_FOR_PAUSE_EVENT
					.getMissingDataIdentifierKey());
			}
		}
	}

	private static void populateIpoAndOtmMandateSpecificFieldsInMandateMetaData(final TransactionHistoryDetails thd,
			final MandateActionEnum mandateAction, final MandateMetaData mandateMetaData) {
		if (MandateActionEnum.COLLECT.equals(mandateAction)) {
			if (ClientStatusEnum.SUCCESS.equals(thd.getStatus())) {
				mandateMetaData.setAllotmentDate(Long.parseLong(thd.getTxnDate()));
			}
			else if (ClientStatusEnum.FAILURE.equals(thd.getStatus())) {
				mandateMetaData.setCancellationDate(Long.parseLong(thd.getTxnDate()));
			}
		}
	}

	private static ParticipantData getPayerData(final TransactionHistoryDetails thd, final TxnParticipants participant,
			final Set<Integer> missingDataIdentifier) {
		ParticipantData payerData = new ParticipantData();
		AccountData payerAccountData = new AccountData();
		MandatePaymentSystemEnum paymentSystem = getPaymentSystem(thd);
		payerData.setPaymentSystem(paymentSystem.getPaymentSystemKey());
		payerData.setName(participant.getName());

		if (Objects.nonNull(participant.getUpiData()) && StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
			payerAccountData.setVpa(participant.getUpiData().getVpa());
		}
		else {
			log.warn("MissingData : payerVpa is missing in mandate event. thd : {}", thd);
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.PAYER_VPA_MISSING.getMissingDataIdentifierKey());
		}
		if (Objects.nonNull(participant.getBankData())) {
			if (StringUtils.isNotBlank(participant.getBankData().getAccNum())) {
				payerAccountData.setAccNum(participant.getBankData().getAccNum());
			}
			else {
				log.warn("MissingData : payer masked account number is missing in mandate event. thd : {}", thd);
				missingDataIdentifier
					.add(MandateMissingDataIdentifierEnum.PAYER_ACC_NUM_MISSING.getMissingDataIdentifierKey());
			}
			if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
				payerAccountData.setBankName(participant.getBankData().getBankName());
			}
			else {
				log.warn("MissingData : payer bank name is missing in mandate event. thd : {}", thd);
				missingDataIdentifier
					.add(MandateMissingDataIdentifierEnum.PAYER_BANK_NAME_MISSING.getMissingDataIdentifierKey());
			}
			if (StringUtils.isNotBlank(participant.getBankData().getIfsc())) {
				payerAccountData.setIfsc(participant.getBankData().getIfsc());
			}
			else {
				log.warn("MissingData : payer bank ifsc is missing in mandate event. thd : {}", thd);
				missingDataIdentifier
					.add(MandateMissingDataIdentifierEnum.PAYER_BANK_IFSC_MISSING.getMissingDataIdentifierKey());
			}
		}
		else {
			log.warn("MissingData : payer bankData is missing in mandate event. thd : {}", thd);
			missingDataIdentifier
				.add(MandateMissingDataIdentifierEnum.PAYER_BANK_DATA_MISSING.getMissingDataIdentifierKey());
		}
		payerData.setAccountData(payerAccountData);
		return payerData;
	}

	private static ParticipantData getPayeeData(final TransactionHistoryDetails thd, final TxnParticipants participant,
			final Set<Integer> missingDataIdentifier) {
		ParticipantData payeeData = new ParticipantData();
		AccountData payeeAccountData = new AccountData();
		MerchantData merchantData = new MerchantData();
		payeeData.setName(participant.getName());

		if (Objects.nonNull(participant.getMerchantData())) {
			merchantData.setMccCode(participant.getMerchantData().getMccCode());
			if (Objects.nonNull(participant.getMerchantData().getMerchantType())) {
				merchantData.setMerchantType(participant.getMerchantData().getMerchantType().getMerchantTypeKey());
			}
			payeeData.setMerchantData(merchantData);
		}

		if (Objects.nonNull(participant.getUpiData()) && StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
			payeeAccountData.setVpa(participant.getUpiData().getVpa());
		}
		else {
			log.warn("MissingData : payeeVpa is missing in mandate event. thd : {}", thd);
			missingDataIdentifier.add(MandateMissingDataIdentifierEnum.PAYEE_VPA_MISSING.getMissingDataIdentifierKey());
		}
		payeeData.setAccountData(payeeAccountData);
		return payeeData;
	}

	// As of now mandate activation through upi via cc is not live, so the below method
	// would be returning UPI only
	private static MandatePaymentSystemEnum getPaymentSystem(final TransactionHistoryDetails thd) {
		if (Objects.nonNull(thd.getContextMap()) && TRUE.equalsIgnoreCase(thd.getContextMap().get(UPI_VIA_CC_FLAG))) {
			return MandatePaymentSystemEnum.UPI_CC;
		}
		return MandatePaymentSystemEnum.UPI;

	}

	public static MandateStatusEnum getMandateStatus(final TransactionHistoryDetails thd) {

		String status = thd.getStatus().name();
		String mandateAction = com.org.panaroma.commons.utility.MandateUtility.getMandateAction(thd.getContextMap())
			.name();
		String identifier = mandateAction + UNDERSCORE + status;

		if (TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
			return getMandateStatusForRecurringMandate(identifier);
		}

		if (TransactionTypeEnum.IPO_MANDATE.equals(thd.getTxnType())) {
			return getMandateStatusForIpoMandate(identifier);
		}

		if (TransactionTypeEnum.SBMD_MANDATE.equals(thd.getTxnType())) {
			return getMandateStatusForSbmdMandate(identifier);
		}

		if (TransactionTypeEnum.ONE_TIME_MANDATE.equals(thd.getTxnType())) {
			return getMandateStatusForOneTimeMandate(identifier);
		}

		return null;
	}

	private static MandateStatusEnum getMandateStatusForRecurringMandate(final String identifier) {
		return switch (identifier) {
			case "CREATE_FAILURE" -> MandateStatusEnum.SETUP_FAILED;
			case "CREATE_PENDING" -> MandateStatusEnum.SETUP_PENDING;
			case "CREATE_SUCCESS", "UNPAUSE_SUCCESS", "PAUSE_PENDING", "PAUSE_FAILURE", "COLLECT_SUCCESS",
					"COLLECT_PENDING", "COLLECT_FAILURE", "IN_PORT_SUCCESS" ->
				MandateStatusEnum.ACTIVE;
			case "PAUSE_SUCCESS", "UNPAUSE_PENDING", "UNPAUSE_FAILURE" -> MandateStatusEnum.PAUSED;
			case "REVOKE_SUCCESS" -> MandateStatusEnum.CANCELLED;
			case "EXPIRE_SUCCESS" -> MandateStatusEnum.EXPIRED;
			case "OUT_PORT_SUCCESS" -> MandateStatusEnum.PORTED;
			// Port-In event status mappings
			case "IN_PORT_PENDING" -> MandateStatusEnum.SETUP_PENDING; // Movement under process
			case "IN_PORT_FAILURE" -> MandateStatusEnum.SETUP_FAILED;  // Transfer failed
			default -> null;
		};
	}

	private static MandateStatusEnum getMandateStatusForIpoMandate(final String identifier) {
		return switch (identifier) {
			case "CREATE_FAILURE" -> MandateStatusEnum.SETUP_FAILED;
			case "CREATE_PENDING" -> MandateStatusEnum.SETUP_PENDING;
			case "CREATE_SUCCESS", "REVOKE_PENDING", "COLLECT_PENDING", "UPDATE_FAILURE", "UPDATE_PENDING",
					"UPDATE_SUCCESS" ->
				MandateStatusEnum.ACTIVE;
			case "COLLECT_FAILURE", "REVOKE_SUCCESS" -> MandateStatusEnum.CANCELLED;
			case "COLLECT_SUCCESS" -> MandateStatusEnum.COMPLETED;
			case "EXPIRE_SUCCESS" -> MandateStatusEnum.EXPIRED;
			default -> null;
		};
	}

	private static MandateStatusEnum getMandateStatusForSbmdMandate(final String identifier) {
		return switch (identifier) {
			case "CREATE_FAILURE" -> MandateStatusEnum.SETUP_FAILED;
			case "CREATE_PENDING" -> MandateStatusEnum.SETUP_PENDING;
			case "CREATE_SUCCESS", "COLLECT_SUCCESS", "COLLECT_PENDING", "COLLECT_FAILURE" -> MandateStatusEnum.ACTIVE;
			case "REVOKE_SUCCESS" -> MandateStatusEnum.CANCELLED;
			case "EXPIRE_SUCCESS" -> MandateStatusEnum.EXPIRED;
			default -> null;
		};
	}

	private static MandateStatusEnum getMandateStatusForOneTimeMandate(final String identifier) {
		return switch (identifier) {
			case "CREATE_FAILURE" -> MandateStatusEnum.SETUP_FAILED;
			case "CREATE_PENDING" -> MandateStatusEnum.SETUP_PENDING;
			case "CREATE_SUCCESS", "REVOKE_PENDING", "COLLECT_PENDING" -> MandateStatusEnum.ACTIVE;
			case "COLLECT_FAILURE", "REVOKE_SUCCESS" -> MandateStatusEnum.CANCELLED;
			case "COLLECT_SUCCESS" -> MandateStatusEnum.COMPLETED;
			case "EXPIRE_SUCCESS" -> MandateStatusEnum.EXPIRED;
			default -> null;
		};
	}

	public static boolean isForMandateBackFilling(final TransactionHistoryDetails thd) {
		return Objects.nonNull(thd) && Objects.nonNull(thd.getContextMap())
				&& TRUE.equalsIgnoreCase(thd.getContextMap().get(IS_BACKFILLING_FOR_MANDATE_DB));
	}

	public static boolean isForMandateBackFilling(final MandateBaseDto mandateBaseDto) {
		return Objects.nonNull(mandateBaseDto) && Objects.nonNull(mandateBaseDto.getMetaDataMap())
				&& MandateBackFillingIdentifierEnum.TSP_DATA_MIGRATION.getBackFillingIdentifierKey()
					.equals(mandateBaseDto.getMetaDataMap().get(BACK_FILLING_IDENTIFIER_MANDATE));
	}

	public static void addMandateBackFillingIdentifier(final MandateBaseDto mandateBaseDto) {
		if (Objects.isNull(mandateBaseDto)) {
			return;
		}
		if (Objects.isNull(mandateBaseDto.getMetaDataMap())) {
			mandateBaseDto.setMetaDataMap(new HashMap<>());
		}
		mandateBaseDto.getMetaDataMap()
			.put(BACK_FILLING_IDENTIFIER_MANDATE,
					MandateBackFillingIdentifierEnum.TSP_DATA_MIGRATION.getBackFillingIdentifierKey());
	}

	public static void addMandateBackFillingIdentifierForRelativeDoc(final MandateBaseDto mandateBaseDto) {
		if (Objects.isNull(mandateBaseDto)) {
			return;
		}
		if (Objects.isNull(mandateBaseDto.getMetaDataMap())) {
			mandateBaseDto.setMetaDataMap(new HashMap<>());
		}
		mandateBaseDto.getMetaDataMap()
			.put(BACK_FILLING_IDENTIFIER_MANDATE,
					MandateBackFillingIdentifierEnum.TSP_DATA_MIGRATION_RELATIVE_DOC_UPDATED
						.getBackFillingIdentifierKey());
	}

	public static void addMandateBackFillingIdentifierForAlteringParentInfo(final MandateBaseDto mandateBaseDto) {
		if (Objects.isNull(mandateBaseDto)) {
			return;
		}
		if (Objects.isNull(mandateBaseDto.getMetaDataMap())) {
			mandateBaseDto.setMetaDataMap(new HashMap<>());
		}
		mandateBaseDto.getMetaDataMap()
			.put(BACK_FILLING_IDENTIFIER_MANDATE,
					MandateBackFillingIdentifierEnum.TSP_DATA_MIGRATION_ALREADY_PRESENT_PARENT_UPDATE
						.getBackFillingIdentifierKey());
	}

}
