{"index_patterns": ["stage3-mandate-info-*"], "settings": {"number_of_shards": 24}, "mappings": {"_doc": {"_routing": {"required": true}, "dynamic": false, "properties": {"entityId": {"type": "keyword"}, "amount": {"type": "double", "index": false}, "type": {"type": "keyword"}, "status": {"type": "keyword"}, "umn": {"type": "keyword"}, "metaDataMap": {"type": "object", "dynamic": "false", "properties": {"ifr": {"type": "keyword"}, "backfillingIdentifier": {"type": "keyword"}}}, "mandateMetaData": {"type": "object", "dynamic": "false", "properties": {"validityStartDate": {"type": "date", "index": false}, "validityEndDate": {"type": "date", "index": false}, "pauseEndDate": {"type": "date", "index": false}, "cancellationDate": {"type": "date", "index": false}, "allotmentDate": {"type": "date", "index": false}, "frequency": {"type": "keyword", "index": false}, "isBackFilled": {"type": "keyword"}}}, "txnDate": {"type": "date"}, "updatedDate": {"type": "date"}, "docCreatedDate": {"type": "date"}, "docUpdatedDate": {"type": "date"}, "createTxnId": {"type": "keyword"}, "payerData": {"properties": {"name": {"type": "text", "index": false}, "paymentSystem": {"type": "keyword"}, "accountData": {"properties": {"vpa": {"type": "keyword"}, "accNum": {"type": "keyword", "index": false}, "bankName": {"type": "keyword", "index": false}, "ifsc": {"type": "keyword", "index": false}}}}}, "payeeData": {"properties": {"name": {"type": "text", "index": false}, "merchantData": {"properties": {"mccCode": {"type": "keyword", "index": false}, "merchantType": {"type": "keyword", "index": false}}}, "accountData": {"properties": {"vpa": {"type": "keyword", "index": false}}}}}, "missingDataIdentifier": {"type": "keyword"}, "createEventType": {"type": "keyword"}}}}, "aliases": {"stage3_mandate_info_alias": {}}}